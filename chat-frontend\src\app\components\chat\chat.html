<div class="chat-container">
  <!-- Username Prompt -->
  <div *ngIf="showUsernamePrompt" class="username-prompt">
    <h2>Enter Your Name</h2>
    <div class="input-area">
      <input
        type="text"
        [(ngModel)]="username"
        placeholder="Enter your name (e.g., User1 or User2)"
        (keypress)="onKeyPress($event)"
        class="username-input"
      />
      <button (click)="setUsername()" [disabled]="!username.trim()">
        Connect
      </button>
    </div>
  </div>

  <!-- Chat Interface -->
  <div *ngIf="!showUsernamePrompt" class="chat-interface">
    <h2>Chat UI - {{ username }}</h2>

    <!-- Connection Status -->
    <div
      *ngIf="showConnectionStatus"
      class="connection-status"
      [class.connected]="isConnected"
      [class.disconnected]="!isConnected"
    >
      {{ isConnected ? 'Connected' : 'Connecting...' }}
    </div>

    <!-- Message Input -->
    <div class="input-area">
      <input
        type="text"
        [(ngModel)]="messageContent"
        placeholder="Type your message"
        (keypress)="onKeyPress($event)"
        [disabled]="!isConnected"
        id="message"
      />
      <button
        (click)="sendMessage()"
        [disabled]="!messageContent.trim() || !isConnected"
      >
        Send
      </button>
    </div>

    <!-- Chat Log -->
    <div class="chat-messages" id="chatLog">
      <div
        *ngFor="let message of messages; trackBy: trackByMessage"
        class="message-wrapper"
        [class.sent]="message.isOwnMessage"
        [class.received]="!message.isOwnMessage"
      >
        <div class="message-bubble">
          <div class="message-sender" *ngIf="!message.isOwnMessage">{{ message.sender }}</div>
          <div class="message-content">{{ message.content }}</div>
        </div>
      </div>
      <div *ngIf="messages.length === 0" class="no-messages">
        No messages yet. Start the conversation!
      </div>
    </div>
  </div>
</div>
