import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatService } from '../../services/chat.service';
import { ChatMessageDisplay } from '../../models/chat-message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chat',
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.html',
  styleUrl: './chat.css'
})
export class Chat implements OnInit, OnDestroy {
  username: string = '';
  messageContent: string = '';
  messages: ChatMessageDisplay[] = [];
  isConnected: boolean = false;
  showUsernamePrompt: boolean = true;
  showConnectionStatus: boolean = true;
  isConnecting: boolean = false;

  private messagesSubscription?: Subscription;
  private connectionSubscription?: Subscription;

  constructor(private chatService: ChatService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.connectionSubscription = this.chatService.connectionStatus$.subscribe(
      status => {
        console.log('🔄 Connection status changed to:', status);
        this.isConnected = status;

        
        this.showConnectionStatus = true;

       
        if (!status && !this.isConnecting) {
          
          setTimeout(() => {
            this.showUsernamePrompt = true;
            this.username = ''; 
            this.messages = []; 
          }, 2000); 
        }

        
        if (status) {
          this.isConnecting = false;
        }

        if (status) {
          setTimeout(() => {
            this.showConnectionStatus = false;
            this.cdr.detectChanges();
          }, 3000);
        } else {
          
          setTimeout(() => {
            this.showConnectionStatus = false;
            this.cdr.detectChanges();
          }, 2000);
        }

        this.cdr.detectChanges();
      }
    );

    this.messagesSubscription = this.chatService.messages$.subscribe(
      messages => {
        this.messages = messages.map(msg => ({
          ...msg,
          isOwnMessage: msg.sender === this.username
        }));
        this.cdr.detectChanges();
      }
    );

    
    window.addEventListener('beforeunload', () => {
      this.chatService.disconnect();
    });
  }

  ngOnDestroy(): void {
    this.messagesSubscription?.unsubscribe();
    this.connectionSubscription?.unsubscribe();
    this.chatService.disconnect();
  }

  async setUsername(): Promise<void> {
    if (this.username.trim()) {
      const trimmedUsername = this.username.trim();

      
      if (this.chatService.isAnotherUserLoggedIn(trimmedUsername)) {
        const activeUser = this.chatService.getActiveUser();
        const confirmMessage = `Another user "${activeUser}" is already logged in from this browser.\n\nOnly one user can be active at a time. Do you want to disconnect "${activeUser}" and login as "${trimmedUsername}"?`;

        if (confirm(confirmMessage)) {
          
          console.log('👤 User chose to disconnect previous user and login');
          this.chatService.disconnectActiveUser();

          
          await new Promise(resolve => setTimeout(resolve, 500));

          
          this.showUsernamePrompt = false;
          this.isConnecting = true;
          const connected = await this.chatService.connect(trimmedUsername);
          this.isConnecting = false;

          if (!connected) {
            console.error('❌ Failed to connect after disconnecting previous user');
            this.showUsernamePrompt = true;
            alert('Failed to connect. Please try again.');
          }
        } else {
          
          console.log('❌ User cancelled login, staying on prompt');
          return;
        }
      } else {
       
        this.showUsernamePrompt = false;
        this.isConnecting = true;
        const connected = await this.chatService.connect(trimmedUsername);
        this.isConnecting = false;

        if (!connected) {
          console.error('❌ Failed to connect');
          this.showUsernamePrompt = true;
          alert('Failed to connect. Please try again.');
        }
      }
    }
  }

  sendMessage(): void {
    if (this.messageContent.trim() && this.isConnected) {
      this.chatService.sendMessage(this.messageContent);
      this.messageContent = '';
    }
  }

  async onKeyPress(event: KeyboardEvent): Promise<void> {
    if (event.key === 'Enter') {
      if (this.showUsernamePrompt) {
        await this.setUsername();
      } else {
        this.sendMessage();
      }
    }
  }

  trackByMessage(index: number, message: ChatMessageDisplay): string {
    return `${message.sender}-${message.content}-${index}`;
  }
}
