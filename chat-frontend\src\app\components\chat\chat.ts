import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatService } from '../../services/chat.service';
import { ChatMessageDisplay } from '../../models/chat-message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chat',
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.html',
  styleUrl: './chat.scss'
})
export class Chat implements OnInit, OnDestroy {
  username: string = '';
  messageContent: string = '';
  messages: ChatMessageDisplay[] = [];
  isConnected: boolean = false;
  showUsernamePrompt: boolean = true;

  private messagesSubscription?: Subscription;
  private connectionSubscription?: Subscription;

  constructor(private chatService: ChatService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.connectionSubscription = this.chatService.connectionStatus$.subscribe(
      status => {
        console.log('Connection status changed:', status);
        this.isConnected = status;
        this.cdr.detectChanges(); // Force change detection
      }
    );

    this.messagesSubscription = this.chatService.messages$.subscribe(
      messages => {
        console.log('Messages updated:', messages);
        console.log('Current username:', this.username);
        this.messages = messages.map(msg => ({
          ...msg,
          isOwnMessage: msg.sender === this.username
        }));
        console.log('Processed messages:', this.messages);
        this.cdr.detectChanges(); // Force change detection for messages too
      }
    );
  }

  ngOnDestroy(): void {
    this.messagesSubscription?.unsubscribe();
    this.connectionSubscription?.unsubscribe();
    this.chatService.disconnect();
  }

  async setUsername(): Promise<void> {
    if (this.username.trim()) {
      this.showUsernamePrompt = false;
      await this.chatService.connect(this.username.trim());
    }
  }

  sendMessage(): void {
    if (this.messageContent.trim() && this.isConnected) {
      console.log('Sending message:', this.messageContent, 'from user:', this.username);
      this.chatService.sendMessage(this.messageContent);
      this.messageContent = '';
    }
  }

  async onKeyPress(event: KeyboardEvent): Promise<void> {
    if (event.key === 'Enter') {
      if (this.showUsernamePrompt) {
        await this.setUsername();
      } else {
        this.sendMessage();
      }
    }
  }

  trackByMessage(index: number, message: ChatMessageDisplay): string {
    return `${message.sender}-${message.content}-${index}`;
  }
}
