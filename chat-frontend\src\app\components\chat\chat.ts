import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatService } from '../../services/chat.service';
import { ChatMessageDisplay } from '../../models/chat-message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chat',
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.html',
  styleUrl: './chat.scss'
})
export class Chat implements OnInit, OnDestroy {
  username: string = '';
  messageContent: string = '';
  messages: ChatMessageDisplay[] = [];
  isConnected: boolean = false;
  showUsernamePrompt: boolean = true;
  showConnectionStatus: boolean = true;

  private messagesSubscription?: Subscription;
  private connectionSubscription?: Subscription;

  constructor(private chatService: ChatService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.connectionSubscription = this.chatService.connectionStatus$.subscribe(
      status => {
        this.isConnected = status;

       
        this.showConnectionStatus = true;

        
        if (status) {
          setTimeout(() => {
            this.showConnectionStatus = false;
            this.cdr.detectChanges();
          }, 3000);
        }

        this.cdr.detectChanges(); 
      }
    );

    this.messagesSubscription = this.chatService.messages$.subscribe(
      messages => {
        this.messages = messages.map(msg => ({
          ...msg,
          isOwnMessage: msg.sender === this.username
        }));
        this.cdr.detectChanges();
      }
    );

    // Clean up localStorage when browser/tab is closed
    window.addEventListener('beforeunload', () => {
      this.chatService.disconnect();
    });
  }

  ngOnDestroy(): void {
    this.messagesSubscription?.unsubscribe();
    this.connectionSubscription?.unsubscribe();
    this.chatService.disconnect();
  }

  async setUsername(): Promise<void> {
    if (this.username.trim()) {
      const trimmedUsername = this.username.trim();

      // Check if another user is already logged in
      if (this.chatService.isAnotherUserLoggedIn(trimmedUsername)) {
        const activeUser = this.chatService.getActiveUser();
        const confirmMessage = `Another user "${activeUser}" is already logged in from this browser.\n\nOnly one user can be active at a time. Do you want to disconnect "${activeUser}" and login as "${trimmedUsername}"?`;

        if (confirm(confirmMessage)) {
          // Disconnect the previous user first
          console.log('👤 User chose to disconnect previous user and login');
          this.chatService.disconnectActiveUser();

          // Wait a moment for the disconnect to propagate
          await new Promise(resolve => setTimeout(resolve, 500));

          // Now connect the new user
          this.showUsernamePrompt = false;
          const connected = await this.chatService.connect(trimmedUsername);

          if (!connected) {
            console.error('❌ Failed to connect after disconnecting previous user');
            this.showUsernamePrompt = true;
            alert('Failed to connect. Please try again.');
          }
        } else {
          // User cancelled - do not connect
          console.log('❌ User cancelled login, staying on prompt');
          return;
        }
      } else {
        // No other user logged in, proceed normally
        this.showUsernamePrompt = false;
        const connected = await this.chatService.connect(trimmedUsername);

        if (!connected) {
          console.error('❌ Failed to connect');
          this.showUsernamePrompt = true;
          alert('Failed to connect. Please try again.');
        }
      }
    }
  }

  sendMessage(): void {
    if (this.messageContent.trim() && this.isConnected) {
      this.chatService.sendMessage(this.messageContent);
      this.messageContent = '';
    }
  }

  async onKeyPress(event: KeyboardEvent): Promise<void> {
    if (event.key === 'Enter') {
      if (this.showUsernamePrompt) {
        await this.setUsername();
      } else {
        this.sendMessage();
      }
    }
  }

  trackByMessage(index: number, message: ChatMessageDisplay): string {
    return `${message.sender}-${message.content}-${index}`;
  }
}
