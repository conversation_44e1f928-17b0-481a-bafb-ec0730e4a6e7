.chat-container {
  max-width: 600px;
  margin: 50px auto;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  font-family: Arial, sans-serif;
}

.username-prompt {
  text-align: center;

  h2 {
    color: #075e54;
    margin-bottom: 20px;
  }

  .username-input {
    width: 300px;
    max-width: 100%;
  }
}

.chat-interface {
  h2 {
    text-align: center;
    color: #075e54;
    margin-bottom: 10px;
  }
}

.connection-status {
  text-align: center;
  padding: 5px;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 12px;
  font-weight: bold;

  &.connected {
    background-color: #d4edda;
    color: #155724;
  }

  &.disconnected {
    background-color: #f8d7da;
    color: #721c24;
  }
}

.input-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;

  input {
    flex: 1;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    font-size: 14px;

    &:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
  }

  button {
    padding: 10px 15px;
    background-color: #25d366;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;

    &:hover:not(:disabled) {
      background-color: #20bd5f;
    }

    &:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
  }
}

.chat-messages {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  margin: 0;
  background-color: #f0f0f0;
  border-radius: 10px;

  .no-messages {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
  }

  .message-wrapper {
    display: flex;
    margin: 8px 0;
    clear: both;

    &.sent {
      justify-content: flex-end;

      .message-bubble {
        background-color: #dcf8c6;
        border-radius: 18px 18px 4px 18px;
        margin-left: 50px;
      }
    }

    &.received {
      justify-content: flex-start;

      .message-bubble {
        background-color: #ffffff;
        border-radius: 18px 18px 18px 4px;
        border: 1px solid #e0e0e0;
        margin-right: 50px;
      }
    }

    .message-bubble {
      padding: 8px 12px;
      max-width: 70%;
      word-wrap: break-word;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      .message-sender {
        font-size: 12px;
        font-weight: bold;
        color: #075e54;
        margin-bottom: 2px;
      }

      .message-content {
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }
}

// Global body styling
:host {
  display: block;
  min-height: 100vh;
  background-color: #e5ddd5;
}