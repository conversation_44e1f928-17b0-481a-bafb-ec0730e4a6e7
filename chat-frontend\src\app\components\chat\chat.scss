.chat-container {
  max-width: 600px;
  margin: 50px auto;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  font-family: Arial, sans-serif;
}

.username-prompt {
  text-align: center;

  h2 {
    color: #075e54;
    margin-bottom: 20px;
  }

  .username-input {
    width: 300px;
    max-width: 100%;
  }
}

.chat-interface {
  h2 {
    text-align: center;
    color: #075e54;
    margin-bottom: 10px;
  }
}

.connection-status {
  text-align: center;
  padding: 5px;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 12px;
  font-weight: bold;

  &.connected {
    background-color: #d4edda;
    color: #155724;
  }

  &.disconnected {
    background-color: #f8d7da;
    color: #721c24;
  }
}

.input-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;

  input {
    flex: 1;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    font-size: 14px;

    &:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
  }

  button {
    padding: 10px 15px;
    background-color: #25d366;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;

    &:hover:not(:disabled) {
      background-color: #20bd5f;
    }

    &:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
  }
}

#chatLog {
  list-style: none;
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
  margin: 0;

  li {
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 10px;
    width: fit-content;
    max-width: 80%;
    clear: both;
    word-wrap: break-word;

    &.sent {
      margin-left: auto;
      background-color: #dcf8c6;
    }

    &.received {
      margin-right: auto;
      background-color: #ffffff;
      border: 1px solid #e0e0e0;
    }
  }
}

// Global body styling
:host {
  display: block;
  min-height: 100vh;
  background-color: #e5ddd5;
}