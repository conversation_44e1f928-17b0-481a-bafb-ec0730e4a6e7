{"version": 3, "file": "sg.js", "sourceRoot": "", "sources": ["sg.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,WAAW,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,sBAAsB,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,oBAAoB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"sg\",[[\"ND\",\"LK\"],u,u],u,[[\"K\",\"S\",\"T\",\"S\",\"K\",\"P\",\"Y\"],[\"Bk1\",\"Bk2\",\"Bk3\",\"Bk4\",\"Bk5\",\"Lâp\",\"<PERSON>â<PERSON>\"],[\"Bikua-ô<PERSON>\",\"Bïkua-û<PERSON>\",\"Bïkua-ptâ\",\"Bïkua-usïö\",\"Bïkua-okü\",\"Lâpôsö\",\"<PERSON>âyenga\"],[\"Bk1\",\"Bk2\",\"Bk3\",\"Bk4\",\"Bk5\",\"<PERSON>â<PERSON>\",\"<PERSON><PERSON><PERSON>\"]],u,[[\"N\",\"F\",\"M\",\"N\",\"B\",\"F\",\"L\",\"K\",\"M\",\"N\",\"N\",\"K\"],[\"Nye\",\"Ful\",\"<PERSON>bä\",\"Ngu\",\"Bêl\",\"Fön\",\"Len\",\"Kük\",\"Mvu\",\"Ngb\",\"Nab\",\"Kak\"],[\"Nyenye\",\"Fulundïgi\",\"Mbängü\",\"Ngubùe\",\"Bêläwü\",\"Föndo\",\"Lengua\",\"Kükürü\",\"Mvuka\",\"Ngberere\",\"Nabändüru\",\"Kakauka\"]],u,[[\"KnK\",\"NpK\"],u,[\"Kôzo na Krîstu\",\"Na pekô tî Krîstu\"]],1,[6,0],[\"d/M/y\",\"d MMM, y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00;¤-#,##0.00\",\"#E0\"],\"XAF\",\"FCFA\",\"farânga CFA (BEAC)\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}