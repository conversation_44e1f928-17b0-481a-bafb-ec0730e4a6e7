{"version": 3, "file": "su.js", "sourceRoot": "", "sources": ["su.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,eAAe,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,iBAAiB,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"su\",[[\"AM\",\"PM\"],u,u],u,[[\"M\",\"S\",\"S\",\"R\",\"K\",\"J\",\"S\"],[\"Mng\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>m\",\"Sap\"],[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Saptu\"],[\"Mng\",\"<PERSON>\",\"<PERSON>\",\"Reb\",\"Ke<PERSON>\",\"Ju<PERSON>\",\"Sap\"]],u,[[\"J\",\"P\",\"M\",\"A\",\"M\",\"J\",\"<PERSON>\",\"A\",\"<PERSON>\",\"O\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Ags\",\"<PERSON>é<PERSON>\",\"Okt\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"],[\"<PERSON>uari\",\"<PERSON><PERSON>bruari\",\"<PERSON>t\",\"April\",\"<PERSON>éi\",\"Juni\",\"Juli\",\"Agustus\",\"Séptémber\",\"Oktober\",\"<PERSON>pémber\",\"D<PERSON>émber\"]],u,[[\"<PERSON>\",\"M\"],u,u],0,[6,0],[\"d/M/yy\",\"d M<PERSON> y\",\"d M<PERSON>M y\",\"EEEE, d MMMM y\"],[\"H.mm\",\"H.mm.ss\",\"H.mm.ss z\",\"H.mm.ss zzzz\"],[\"{1}, {0}\",u,\"{1} 'jam' {0}\",u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\".\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"IDR\",\"Rp\",\"Rupee Indonésia\",{\"IDR\":[\"Rp\"]},\"ltr\", plural];\n"]}