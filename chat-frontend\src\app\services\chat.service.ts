import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Client } from '@stomp/stompjs';
import { ChatMessage } from '../models/chat-message.model';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private stompClient: Client | null = null;
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  
  public messages$ = this.messagesSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();
  
  private username: string = '';
  private readonly STORAGE_KEY = 'chat_active_user';
  private readonly DISCONNECT_KEY = 'chat_force_disconnect';

  constructor() {
    // Listen for localStorage changes (cross-tab communication)
    window.addEventListener('storage', (event) => {
      if (event.key === this.DISCONNECT_KEY && event.newValue === this.username) {
        console.log('🚨 Force disconnect received for user:', this.username);
        this.forceDisconnect();
      }
    });
  }

  // Check if another user is already logged in
  isAnotherUserLoggedIn(currentUsername: string): boolean {
    const activeUser = localStorage.getItem(this.STORAGE_KEY);
    return activeUser !== null && activeUser !== currentUsername;
  }

  // Get the currently logged in user
  getActiveUser(): string | null {
    return localStorage.getItem(this.STORAGE_KEY);
  }

  // Disconnect the currently active user (called when new user wants to login)
  disconnectActiveUser(): void {
    const activeUser = this.getActiveUser();
    if (activeUser) {
      console.log('🔄 Disconnecting active user:', activeUser);
      this.forceDisconnectUser(activeUser);
    }
  }

  // Set the active user in localStorage
  private setActiveUser(username: string): void {
    localStorage.setItem(this.STORAGE_KEY, username);
  }

  // Clear the active user from localStorage
  private clearActiveUser(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  // Force disconnect a user (used for cross-tab communication)
  private forceDisconnectUser(username: string): void {
    localStorage.setItem(this.DISCONNECT_KEY, username);
    // Clear the disconnect signal after a short delay
    setTimeout(() => {
      localStorage.removeItem(this.DISCONNECT_KEY);
    }, 1000);
  }

  // Handle force disconnect (when this user is being kicked out)
  private forceDisconnect(): void {
    console.log('🚨 This user is being forcefully disconnected');
    if (this.stompClient) {
      this.stompClient.deactivate();
      this.stompClient = null;
    }
    this.clearActiveUser();
    this.connectionStatusSubject.next(false);
    this.messagesSubject.next([]);
    this.username = ''; // Clear username to reset state

    // Show alert to user
    alert('You have been disconnected because another user logged in from a different tab/window.');
  }

  async connect(username: string): Promise<boolean> {
    this.username = username;
    console.log('Starting connection for user:', username);

    // Check if another user is logged in
    if (this.isAnotherUserLoggedIn(username)) {
      console.log('❌ Another user is already logged in, connection blocked');
      return false;
    }

    // Set this user as active in localStorage
    this.setActiveUser(username);

    try {

      this.connectionStatusSubject.next(false);

     
      const SockJSModule = await import('sockjs-client');
      const SockJS = SockJSModule.default;

      
      const socket = new SockJS('http://localhost:8080/ws');

      this.stompClient = new Client({
        webSocketFactory: () => socket,
        debug: (str) => {
          console.log('STOMP Debug: ', str);
        },
        onConnect: (frame) => {
          console.log('✅ STOMP Connected successfully!', frame);
          console.log('Setting connection status to TRUE');
          this.connectionStatusSubject.next(true);
          console.log('Current connection status value:', this.connectionStatusSubject.value);

         
          this.stompClient?.subscribe('/topic/messages', (message) => {
            console.log('📨 Received message:', message.body);
            const chatMessage: ChatMessage = JSON.parse(message.body);
            this.addMessage(chatMessage);
          });

        
          if (this.stompClient) {
            console.log('📜 Requesting message history...');
            this.stompClient.publish({
              destination: '/app/chat.history',
              body: JSON.stringify({ user: this.username })
            });
          }
        },
        onDisconnect: () => {
          console.log('❌ Disconnected from WebSocket');
          this.connectionStatusSubject.next(false);
          this.clearActiveUser();
        },
        onStompError: (frame) => {
          console.error('💥 STOMP Error: ', frame);
          this.connectionStatusSubject.next(false);
        },
        onWebSocketError: (error) => {
          console.error('🔌 WebSocket Error:', error);
          this.connectionStatusSubject.next(false);
        }
      });

      console.log('🚀 Activating STOMP client...');
      this.stompClient.activate();
      return true;
    } catch (error) {
      console.error('❌ Failed to load SockJS:', error);
      this.connectionStatusSubject.next(false);
      return false;
    }
  }

  sendMessage(content: string): void {
    if (this.stompClient && this.stompClient.connected && content.trim() !== '') {
      const message: ChatMessage = {
        sender: this.username,
        content: content.trim(),
        timestamp: new Date()
      };

      console.log('📤 Sending message:', message);
      this.stompClient.publish({
        destination: '/app/chat.send',
        body: JSON.stringify(message)
      });
    } else {
      console.warn('⚠️ Cannot send message - not connected or empty content');
    }
  }



  private addMessage(message: ChatMessage): void {
    const currentMessages = this.messagesSubject.value;
    console.log('➕ Adding new message:', message);
    this.messagesSubject.next([...currentMessages, message]);
  }

  disconnect(): void {
    if (this.stompClient) {
      this.stompClient.deactivate();
      this.stompClient = null;
    }
    this.clearActiveUser();
    this.connectionStatusSubject.next(false);
    this.messagesSubject.next([]);
  }

  getUsername(): string {
    return this.username;
  }

  clearMessages(): void {
    this.messagesSubject.next([]);
  }

  
  getConnectionStatus(): boolean {
    const status = this.connectionStatusSubject.value;
    console.log('Current connection status:', status);
    return status;
  }

 
  forceConnectionStatusUpdate(): void {
    const isConnected = this.stompClient?.connected || false;
    console.log('Force updating connection status to:', isConnected);
    this.connectionStatusSubject.next(isConnected);
  }
}
