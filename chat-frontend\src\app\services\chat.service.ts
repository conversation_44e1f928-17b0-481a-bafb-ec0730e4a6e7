import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { ChatMessage } from '../models/chat-message.model';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private stompClient: Client | null = null;
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  
  public messages$ = this.messagesSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();
  
  private username: string = '';

  constructor() {}

  connect(username: string): void {
    this.username = username;

    // Create WebSocket connection using SockJS
    const socket = new SockJS('http://localhost:8080/ws');

    this.stompClient = new Client({
      webSocketFactory: () => socket,
      debug: (str) => {
        console.log('STOMP Debug: ', str);
      },
      onConnect: () => {
        console.log('Connected to WebSocket');
        this.connectionStatusSubject.next(true);
        
        // Subscribe to the topic for receiving messages
        this.stompClient?.subscribe('/topic/messages', (message) => {
          const chatMessage: ChatMessage = JSON.parse(message.body);
          this.addMessage(chatMessage);
        });
      },
      onDisconnect: () => {
        console.log('Disconnected from WebSocket');
        this.connectionStatusSubject.next(false);
      },
      onStompError: (frame) => {
        console.error('STOMP Error: ', frame);
        this.connectionStatusSubject.next(false);
      }
    });

    this.stompClient.activate();
  }

  sendMessage(content: string): void {
    if (this.stompClient && this.stompClient.connected && content.trim() !== '') {
      const message: ChatMessage = {
        sender: this.username,
        content: content.trim(),
        timestamp: new Date()
      };

      this.stompClient.publish({
        destination: '/app/chat.send',
        body: JSON.stringify(message)
      });
    }
  }

  private addMessage(message: ChatMessage): void {
    const currentMessages = this.messagesSubject.value;
    this.messagesSubject.next([...currentMessages, message]);
  }

  disconnect(): void {
    if (this.stompClient) {
      this.stompClient.deactivate();
      this.stompClient = null;
      this.connectionStatusSubject.next(false);
    }
  }

  getUsername(): string {
    return this.username;
  }

  clearMessages(): void {
    this.messagesSubject.next([]);
  }
}
