/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    EUR: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    HUF: string[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    VND: (string | undefined)[];
    XCD: (string | undefined)[];
} | undefined)[];
export default _default;
