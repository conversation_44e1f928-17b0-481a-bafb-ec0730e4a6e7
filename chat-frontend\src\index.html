<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Chat Application</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <script>
    // Polyfill for global variable (needed for SockJS)
    if (typeof global === 'undefined') {
      var global = window;
    }
  </script>
</head>
<body>
  <app-root></app-root>
</body>
</html>
