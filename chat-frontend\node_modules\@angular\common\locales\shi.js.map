{"version": 3, "file": "shi.js", "sourceRoot": "", "sources": ["shi.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1C,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,QAAQ,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,UAAU,EAAC,OAAO,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,YAAY,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nif (n === Math.floor(n) && (n >= 2 && n <= 10))\n    return 3;\nreturn 5;\n}\n\nexport default [\"shi\",[[\"ⵜⵉⴼⴰⵡⵜ\",\"ⵜⴰⴷⴳⴳⵯⴰⵜ\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"ⴰⵙⴰ\",\"ⴰⵢⵏ\",\"ⴰⵙⵉ\",\"ⴰⴽⵕ\",\"ⴰⴽⵡ\",\"ⴰⵙⵉⵎ\",\"ⴰⵙⵉⴹ\"],[\"ⴰⵙⴰⵎⴰⵙ\",\"ⴰⵢⵏⴰⵙ\",\"ⴰⵙⵉⵏⴰⵙ\",\"ⴰⴽⵕⴰⵙ\",\"ⴰⴽⵡⴰⵙ\",\"ⵙⵉⵎⵡⴰⵙ\",\"ⴰⵙⵉⴹⵢⴰⵙ\"],[\"ⴰⵙⴰ\",\"ⴰⵢⵏ\",\"ⴰⵙⵉ\",\"ⴰⴽⵕ\",\"ⴰⴽⵡ\",\"ⴰⵙⵉⵎ\",\"ⴰⵙⵉⴹ\"]],u,[[\"ⵉ\",\"ⴱ\",\"ⵎ\",\"ⵉ\",\"ⵎ\",\"ⵢ\",\"ⵢ\",\"ⵖ\",\"ⵛ\",\"ⴽ\",\"ⵏ\",\"ⴷ\"],[\"ⵉⵏⵏ\",\"ⴱⵕⴰ\",\"ⵎⴰⵕ\",\"ⵉⴱⵔ\",\"ⵎⴰⵢ\",\"ⵢⵓⵏ\",\"ⵢⵓⵍ\",\"ⵖⵓⵛ\",\"ⵛⵓⵜ\",\"ⴽⵜⵓ\",\"ⵏⵓⵡ\",\"ⴷⵓⵊ\"],[\"ⵉⵏⵏⴰⵢⵔ\",\"ⴱⵕⴰⵢⵕ\",\"ⵎⴰⵕⵚ\",\"ⵉⴱⵔⵉⵔ\",\"ⵎⴰⵢⵢⵓ\",\"ⵢⵓⵏⵢⵓ\",\"ⵢⵓⵍⵢⵓⵣ\",\"ⵖⵓⵛⵜ\",\"ⵛⵓⵜⴰⵏⴱⵉⵔ\",\"ⴽⵜⵓⴱⵔ\",\"ⵏⵓⵡⴰⵏⴱⵉⵔ\",\"ⴷⵓⵊⴰⵏⴱⵉⵔ\"]],u,[[\"ⴷⴰⵄ\",\"ⴷⴼⵄ\"],u,[\"ⴷⴰⵜ ⵏ ⵄⵉⵙⴰ\",\"ⴷⴼⴼⵉⵔ ⵏ ⵄⵉⵙⴰ\"]],1,[6,0],[\"d/M/y\",\"d MMM, y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00¤\",\"#E0\"],\"MAD\",\"MAD\",\"ⴰⴷⵔⵉⵎ ⵏ ⵍⵎⵖⵔⵉⴱ\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}