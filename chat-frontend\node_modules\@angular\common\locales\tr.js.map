{"version": 3, "file": "tr.js", "sourceRoot": "", "sources": ["tr.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,WAAW,EAAC,MAAM,EAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"tr\",[[\"öö\",\"ös\"],[\"ÖÖ\",\"ÖS\"],u],[[\"ÖÖ\",\"ÖS\"],u,u],[[\"P\",\"P\",\"S\",\"Ç\",\"P\",\"C\",\"C\"],[\"<PERSON>\",\"<PERSON>zt\",\"Sal\",\"Çar\",\"Per\",\"Cum\",\"Cmt\"],[\"Pazar\",\"Pazartesi\",\"Salı\",\"Çarşamba\",\"Perşembe\",\"Cuma\",\"Cumartesi\"],[\"<PERSON>\",\"Pt\",\"Sa\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>t\"]],u,[[\"O\",\"<PERSON>\",\"M\",\"N\",\"M\",\"H\",\"T\",\"A\",\"E\",\"E\",\"K\",\"A\"],[\"Oca\",\"<PERSON>ub\",\"Mar\",\"Nis\",\"May\",\"Haz\",\"Tem\",\"Ağu\",\"Eyl\",\"Eki\",\"Kas\",\"Ara\"],[\"Ocak\",\"Şubat\",\"Mart\",\"Nisan\",\"Mayıs\",\"Haziran\",\"<PERSON><PERSON>uz\",\"<PERSON>ğustos\",\"<PERSON>ylül\",\"<PERSON>kim\",\"Kasım\",\"Aralık\"]],u,[[\"MÖ\",\"MS\"],u,[\"Milattan Önce\",\"Milattan Sonra\"]],1,[6,0],[\"d.MM.y\",\"d MMM y\",\"d MMMM y\",\"d MMMM y EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"%#,##0\",\"¤#,##0.00\",\"#E0\"],\"TRY\",\"₺\",\"Türk Lirası\",{\"AUD\":[\"AU$\",\"$\"],\"BYN\":[u,\"р.\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"L\"],\"RUR\":[u,\"р.\"],\"THB\":[\"฿\"],\"TRY\":[\"₺\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}