{"version": 3, "file": "ti.js", "sourceRoot": "", "sources": ["ti.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === Math.floor(n) && (n >= 0 && n <= 1))\n    return 1;\nreturn 5;\n}\n\nexport default [\"ti\",[[\"ቅ.ቀ.\",\"ድ.ቀ.\"],u,u],u,[[\"ሰ\",\"ሰ\",\"ሰ\",\"ረ\",\"ሓ\",\"ዓ\",\"ቀ\"],[\"ሰን\",\"ሰኑ\",\"ሰሉ\",\"ረቡ\",\"ሓሙ\",\"ዓር\",\"ቀዳ\"],[\"ሰንበት\",\"ሰኑይ\",\"ሰሉስ\",\"ረቡዕ\",\"ሓሙስ\",\"ዓርቢ\",\"ቀዳም\"],[\"ሰን\",\"ሰኑ\",\"ሰሉ\",\"ረቡ\",\"ሓሙ\",\"ዓር\",\"ቀዳ\"]],u,[[\"ጥ\",\"ለ\",\"መ\",\"ሚ\",\"ግ\",\"ሰ\",\"ሓ\",\"ነ\",\"መ\",\"ጥ\",\"ሕ\",\"ታ\"],[\"ጥሪ\",\"ለካ\",\"መጋ\",\"ሚያ\",\"ግን\",\"ሰነ\",\"ሓም\",\"ነሓ\",\"መስ\",\"ጥቅ\",\"ሕዳ\",\"ታሕ\"],[\"ጥሪ\",\"ለካቲት\",\"መጋቢት\",\"ሚያዝያ\",\"ግንቦት\",\"ሰነ\",\"ሓምለ\",\"ነሓሰ\",\"መስከረም\",\"ጥቅምቲ\",\"ሕዳር\",\"ታሕሳስ\"]],u,[[\"ዓ/ዓ\",\"ዓ/ም\"],u,[\"ቅድመ ክርስቶስ\",\"ዓመተ ምሕረት\"]],0,[6,0],[\"dd/MM/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE፣ d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,\"{1} ሰዓት {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"ETB\",\"Br\",\"ብር\",{\"CNY\":[u,\"¥\"],\"ETB\":[\"Br\"],\"JPY\":[u,\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}